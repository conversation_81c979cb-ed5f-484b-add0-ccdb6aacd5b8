package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/mark3labs/mcp-go/mcp"
	log "github.com/sirupsen/logrus"

	"gitlab.sz.sensetime.com/fin/nl2sql/nl2sql-api-service/components/common"
)

const (
	limit = 50
)

type ToolData struct {
	Explain string     `json:"explain"`
	Header  []string   `json:"header"`
	Body    [][]string `json:"body"`
}

func MCPToolData(explain string, header []string, body [][]string) string {
	td := &ToolData{
		Explain: explain,
		Header:  header,
		Body:    body,
	}
	tds, _ := json.Marshal(td)
	return string(tds)
}

func (td *ToolData) ToMarkdown(limit int) string {
	if len(td.Header) == 0 && len(td.Body) == 0 {
		return td.Explain
	}
	var sb strings.Builder
	sb.WriteString(td.Explain)
	sb.WriteString("\n")
	if len(td.<PERSON>er) > 0 {
		sb.WriteString("| " + strings.Join(td.Header, " | ") + " |\n")
		sb.WriteString("| " + strings.Repeat("--- | ", len(td.Header)) + "\n")
	}
	body := td.Body
	if limit > 0 && len(body) > limit {
		body = body[:limit]
	}
	for _, row := range body {
		sb.WriteString("| " + strings.Join(row, " | ") + " |\n")
	}
	return sb.String()
}

var mcpToolIndicatorAmount = mcp.NewTool("indicator_amount",
	mcp.WithDescription(`
问题类型: 问指标数量
问题示例：
	现在有多少个指标
	每个分析主题下的指标数量
期望回答:
	现在您权限可查范围内一共有X个分析主题，共计X个指标，每个主题的指标数量如下：
	|分析主题|指标数量|
	| --- | --- |
	|个人存款|30|
	|个人贷款|50| 
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
)

func (s *Service) mcpToolIndicatorAmountHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	topicName := request.GetString("topic_name", "")
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicIndicators := map[string][]*common.Indicator{}
	var topicIndicatorAmount int
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if topicName != "" && topic.PermissionName != topicName {
			// 如果指定了分析主题名称，则只查询该主题的指标
			continue
		}
		indicatorsResp, err := s.Client.GetAnalysisTopicIndicators(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicIndicators[topic.PermissionName] = indicatorsResp.Data
		topicIndicatorAmount += len(indicatorsResp.Data)
	}
	explain := fmt.Sprintf("现在您权限可查范围内一共有%d个分析主题，共计%d个指标，每个主题的指标数量如下：\n", len(topicIndicators), topicIndicatorAmount)
	header := []string{
		"分析主题",
		"指标数量",
	}
	body := [][]string{}
	for currentTopicName, indicators := range topicIndicators {
		body = append(body, []string{
			currentTopicName,
			strconv.Itoa(len(indicators)),
		})
	}
	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}

var mcpToolIndicatorNames = mcp.NewTool("indicator_names",
	mcp.WithDescription(`
问题类型: 问指标名称
问题示例：
	现在有哪些指标
期望回答:
	当前您权限内可查的指标共有X个，清单如下：
	|分析主题|指标名称|
	| --- | --- |
	|个人存款|存款余额|
	|个人贷款|贷款余额|
	|个人贷款|客户数| 
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
)

func (s *Service) mcpToolIndicatorNamesHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	topicName := request.GetString("topic_name", "")
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicIndicators := map[string][]*common.Indicator{}
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if topicName != "" && topic.PermissionName != topicName {
			// 如果指定了分析主题名称，则只查询该主题的指标
			continue
		}
		indicatorsResp, err := s.Client.GetAnalysisTopicIndicators(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicIndicators[topic.PermissionName] = indicatorsResp.Data
	}
	var (
		explain string
		header  = []string{}
		body    = [][]string{}
	)
	if topicName != "" {
		explain = "当前【%s】主题下的指标共有%d个，清单如下：\n"
		explain = fmt.Sprintf(explain, topicName, len(topicIndicators[topicName]))
		header = []string{
			"指标名称",
		}
		for _, indicators := range topicIndicators {
			for _, indicator := range indicators {
				body = append(body, []string{
					indicator.Name,
				})
			}
		}
	} else {
		explain = "当前您权限内可查的指标共有%d个，清单如下：\n"
		header = []string{
			"分析主题",
			"指标名称",
		}
		indicatorCount := 0
		for name, indicators := range topicIndicators {
			for _, indicator := range indicators {
				indicatorCount++
				body = append(body, []string{
					name,
					indicator.Name,
				})
			}
		}
		explain = fmt.Sprintf("当前您权限内可查的指标共有%d个，清单如下：\n", indicatorCount)
	}

	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil

}

var mcpToolIndicatorInfo = mcp.NewTool("indicator_info",
	mcp.WithDescription(`
问题类型: 问指标信息
问题示例：
	个人贷款余额指标是什么含义
	个人贷款余额是怎么算的
	个人贷款余额对应哪个字段
	个人贷款余额指标是什么意思
期望回答:
	为您找到以下指标信息：
	|指标名称|所属分析主题|分析模型|字段|业务口径|计算公式|筛选条件|
	| --- | --- | --- | --- | --- | --- | --- |
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
	mcp.WithArray("indicator_names",
		mcp.Required(),
		mcp.Description("指标名称数组"),
		mcp.WithStringItems(mcp.MinLength(1)),
	),
)

func (s *Service) mcpToolIndicatorInfoHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	currentTopicName := request.GetString("topic_name", "")
	indicatorNames := request.GetStringSlice("indicator_names", []string{})
	if len(indicatorNames) == 0 {
		return nil, fmt.Errorf("缺少指标名称参数")
	}
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicIndicators := map[string][]*common.Indicator{}
	topicName2Id := map[string]string{}
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if currentTopicName != "" && currentTopicName != topic.PermissionName {
			// 如果指定了分析主题名称，则只查询该主题的指标
			continue
		}
		indicatorsResp, err := s.Client.GetAnalysisTopicIndicators(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicIndicators[topic.PermissionName] = indicatorsResp.Data
		topicName2Id[topic.PermissionName] = topicKeys[1]
	}
	explain := "为您找到以下指标信息：\n"
	header := []string{
		"指标名称",
		"所属分析主题",
		"分析模型",
		"字段",
		"业务口径",
		"计算公式",
		"筛选条件",
	}
	body := [][]string{}
	for topicName, indicators := range topicIndicators {
		for _, indicator := range indicators {
			// 检查指标名称是否在请求的数组中
			for _, indicatorName := range indicatorNames {
				if indicator.Name == indicatorName {
					analysisModelName := ""
					analysisModel, err := s.Client.GetAnalysisTopicAnalysisModel(ctx, topicName2Id[topicName], indicator.AnalysisModel) // Dependence: 数据服务每个指标的分析模型是必填的，但是实际为空，所以现兼容。
					if err == nil && analysisModel != nil {
						analysisModelName = analysisModel.Name
					}
					body = append(body, []string{
						indicator.Name,
						topicName,
						analysisModelName,
						indicator.FieldName,
						indicator.BusinessCaliberDescription,
						indicator.Formula,
						indicator.FilterDescription,
					})
					break // 找到匹配的指标后跳出内层循环
				}
			}
		}
	}
	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}

var mcpToolDimensionAmount = mcp.NewTool("dimension_amount",
	mcp.WithDescription(`
问题类型: 问维度数量
问题示例：
	维度有多少
期望回答:
	现在您权限可查范围内一共有X个分析主题，共计X个维度，每个主题的维度数量如下：
	|分析主题|维度数量|
	| --- | --- |
	|个人存款|30|
	|个人贷款|50| 
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
)

func (s *Service) mcpToolDimensionAmountHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	topicName := request.GetString("topic_name", "")
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicDimensions := map[string][]*common.Dimension{}
	var topicDimensionAmount int
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if topicName != "" && topic.PermissionName != topicName {
			// 如果指定了分析主题名称，则只查询该主题的维度
			continue
		}
		resp, err := s.Client.GetAnalysisTopicDimensions(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicDimensions[topic.PermissionName] = resp.Data
		topicDimensionAmount += len(resp.Data)
	}
	var (
		explain string
		header  = []string{}
		body    = [][]string{}
	)
	if topicName != "" {
		explain = fmt.Sprintf("当前【%s】主题下的维度共有%d个，清单如下：\n", topicName, len(topicDimensions[topicName]))
		header = []string{
			"维度名称",
		}
		for _, dimensions := range topicDimensions {
			for _, dimension := range dimensions {
				body = append(body, []string{
					dimension.Name,
				})
			}
		}
	} else {
		explain = fmt.Sprintf("现在您权限可查范围内一共有%d个分析主题，共计%d个维度，每个主题的指标数量如下：\n", len(topicDimensions), topicDimensionAmount)
		header = []string{
			"分析主题",
			"维度数量",
		}
		for currentTopicName, dimensions := range topicDimensions {
			body = append(body, []string{
				currentTopicName,
				strconv.Itoa(len(dimensions)),
			})
		}
	}

	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}

var mcpToolDimensionNames = mcp.NewTool("dimension_names",
	mcp.WithDescription(`
问题类型: 问维度名称
问题示例：
	维度有哪些
期望回答:
	当前您权限内可查的维度共有X个，清单如下：
	|分析主题|维度名称|
	| --- | --- |
	|个人存款|机构、产品类型、XXX、XXX|
	|个人贷款|机构、产品类型、XXX、XXX|
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
)

func (s *Service) mcpToolDimensionNamesHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	topicName := request.GetString("topic_name", "")
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicDimensions := map[string][]*common.Dimension{}
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if topicName != "" && topic.PermissionName != topicName {
			// 如果指定了分析主题名称，则只查询该主题的维度
			continue
		}
		dimensionsResp, err := s.Client.GetAnalysisTopicDimensions(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicDimensions[topic.PermissionName] = dimensionsResp.Data
	}
	var (
		explain string
		header  = []string{}
		body    = [][]string{}
	)
	if topicName != "" {
		explain = fmt.Sprintf("当前【%s】主题下的维度共有%d个，清单如下：\n", topicName, len(topicDimensions[topicName]))
		header = []string{
			"维度名称",
		}
		for _, dimensions := range topicDimensions {
			for _, dimension := range dimensions {
				body = append(body, []string{
					dimension.Name,
				})
			}
		}
	} else {
		explain = "当前您权限内可查的维度共有%d个，清单如下：\n"
		header = []string{
			"分析主题",
			"维度名称",
		}
		indicatorCount := 0
		for currentTopicName, dimensions := range topicDimensions {
			for _, dimension := range dimensions {
				body = append(body, []string{
					currentTopicName,
					dimension.Name,
				})
				indicatorCount++
			}
		}
		explain = fmt.Sprintf(explain, indicatorCount)
	}

	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}

var mcpToolDimensionInfo = mcp.NewTool("dimension_info",
	mcp.WithDescription(`
问题类型: 问维度取值
问题示例：
	机构有哪些
	产品类型有哪些
期望回答:
	为您找到以下维度信息：
	|分析主题|维度名称|维度取值|
	| --- | --- | --- |
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
	mcp.WithString("topic_name",
		mcp.Description("分析主题名称"),
	),
	mcp.WithArray("dimension_names",
		mcp.Required(),
		mcp.Description("维度名称数组"),
		mcp.WithStringItems(mcp.MinLength(1)),
	),
)

func (s *Service) mcpToolDimensionInfoHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	topicName := request.GetString("topic_name", "")
	dimensionNames := request.GetStringSlice("dimension_names", []string{})
	if len(dimensionNames) == 0 {
		return nil, fmt.Errorf("缺少维度名称参数")
	}
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	topicDimensions := map[string][]*common.Dimension{}
	topicName2Id := map[string]string{}
	for _, topic := range topics {
		topicKeys := strings.Split(topic.PermissionKey, "_")
		if len(topicKeys) != 2 {
			log.Errorf("权限key格式错误: %s", topic.PermissionKey)
			continue
		}
		if topicName != "" && topic.PermissionName != topicName {
			// 如果指定了分析主题名称，则只查询该主题的维度
			continue
		}
		dimensionsResp, err := s.Client.GetAnalysisTopicDimensions(ctx, topicKeys[1])
		if err != nil {
			log.Errorf("获取指标失败: %v", err)
			return nil, err
		}
		topicDimensions[topic.PermissionName] = dimensionsResp.Data
		topicName2Id[topic.PermissionName] = topicKeys[1]
	}
	var (
		explain string
		header  = []string{}
		body    = [][]string{}
	)
	if topicName != "" {
		explain = fmt.Sprintf("当前【%s】主题下的维度取值如下：\n", topicName)
	} else {
		explain = "为您找到以下维度信息：\n"
	}
	header = []string{
		"分析主题",
		"维度名称",
		"维度取值",
	}
	for currentTopicName, dimensions := range topicDimensions {
		for _, dimension := range dimensions {
			// 检查维度名称是否在请求的数组中
			for _, dimensionName := range dimensionNames {
				if dimension.Name == dimensionName {
					body = append(body, []string{
						currentTopicName,
						dimension.Name,
						dimension.EnumValues,
					})
					break // 找到匹配的维度后跳出内层循环
				}
			}
		}
	}
	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}

var mcpToolAnalysisTopicAmount = mcp.NewTool("analysis_topic_amount",
	mcp.WithDescription(`
问题类型: 问分析主题数量
问题示例：
	现在有多少个分析主题
期望回答:
	您当前权限内的分析主题数量为3个
`),
	mcp.WithString("user_id",
		mcp.Required(),
		mcp.Description("用户ID"),
		mcp.MinLength(1),
	),
)

func (s *Service) mcpToolAnalysisTopicAmountHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	userId := request.GetString("user_id", "")
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	topics, err := s.UserDataPermissions(ctx, userId)
	if err != nil {
		log.Errorf("获取用户数据权限失败: %v", err)
		return nil, err
	}
	explain := fmt.Sprintf("您当前权限内的分析主题数量为%d个", len(topics))
	return mcp.NewToolResultText(MCPToolData(explain, []string{}, [][]string{})), nil
}

var mcpToolDataSourceAmount = mcp.NewTool("data_source_amount",
	mcp.WithDescription(`
问题类型: 问数据表
问题示例：
	有哪些数据表
期望回答:
	共找到以下X张数据表：
`),
)

func (s *Service) mcpToolDataSourceAmountHandler(ctx context.Context, request mcp.CallToolRequest) (*mcp.CallToolResult, error) {
	ctx, cancel := context.WithTimeout(ctx, 10*time.Second)
	defer cancel()
	resp, err := s.Client.GetDataSources(ctx)
	if err != nil {
		log.Errorf("获取数据源失败: %v", err)
		return nil, err
	}

	var (
		explain    string
		header     = []string{}
		body       = [][]string{}
		totalCount int
	)

	if len(resp.DataSources) == 0 {
		explain = "共找到以下 0 张数据表："
		return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
	}

	header = []string{
		"数据库",
		"表英文名",
		"表注释",
	}

	// 遍历每个数据源，获取其下的所有表
	for _, source := range resp.DataSources {
		page := &common.Page{Offset: 0, Limit: 1000}
		tablesResp, err := s.Client.GetDataSourceTables(ctx, fmt.Sprintf("%d", source.Id), page)
		if err != nil {
			log.Errorf("获取数据源 %s 的表失败: %v", source.Name, err)
			continue
		}

		for _, table := range tablesResp.Tables {
			body = append(body, []string{
				table.DbName,
				table.TableName,
				table.TableComment,
			})
			totalCount++
		}
	}

	explain = fmt.Sprintf("共找到以下 %d 张数据表：", totalCount)
	return mcp.NewToolResultText(MCPToolData(explain, header, body)), nil
}
